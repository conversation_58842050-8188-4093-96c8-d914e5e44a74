[FRONTEND] 2025/07/04 23:34:50 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751646889624_0cenqzbso | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:34:50 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646889638_h6xzawquk | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:04 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646904191_tzsyhmcif | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:09 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646906008_jhfwps5yc | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:35:19 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646916376_kd4x4m56d | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:36:39 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751646996599_pnyhia3wt | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:37:36 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647055112_c4kh7wx3r | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:38:40 Level: WARN | Message: NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the "track expression" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956 | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647118740_43xtp8dwr | Details: [
  "NG0956: The configured tracking expression (track by identity) caused re-creation of the entire collection of size 1. This is an expensive operation requiring destruction and subsequent creation of DOM nodes, directives, components etc. Please review the \"track expression\" and make sure that it uniquely identifies items in a collection. Find more at https://angular.dev/errors/NG0956"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:26 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647221461_giazzxyfu | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:26 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647221469_77mbruapb | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:38 Level: WARN | Message: Application started | Context: app_startup | Component: app_startup | URL: wails://localhost:9245/ | TraceID: trace_1751647234735_o2mik9vs1 | Details: Angular application initialized | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
[FRONTEND] 2025/07/04 23:40:38 Level: WARN | Message: validateCurrentProfileIndex: invalid selected_profile_index | Context: console.warn | Component: console.warn | URL: wails://localhost:9245/ | TraceID: trace_1751647234744_rkymzzwee | Details: [
  "validateCurrentProfileIndex: invalid selected_profile_index"
] | UserAgent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io | BrowserInfo: {"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) wails.io","platform":"MacIntel","language":"en-GB","cookieEnabled":true,"onLine":true,"screenWidth":1920,"screenHeight":1080,"windowWidth":768,"windowHeight":768,"timezone":"Asia/Saigon","url":"wails://localhost:9245/","referrer":""}
